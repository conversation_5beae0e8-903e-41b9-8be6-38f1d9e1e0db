package cn.mlamp.insightflow.cms.task.migrations;

import cn.mlamp.insightflow.cms.common.redis.LockService;
import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.mapper.QianchuanMaterialVideoMapper;
import cn.mlamp.insightflow.cms.service.QianchuanMaterialVideoService;
import cn.mlamp.insightflow.cms.util.PinyinUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class QianchuanMaterialVideoMigrationTask {

    @Autowired
    private QianchuanMaterialVideoService materialVideoService;

    @Autowired
    private QianchuanMaterialVideoMapper materialVideoMapper;

    @Autowired
    private LockService lockService;

    // 每小时执行一次
    @Scheduled(fixedDelay = 1000 * 60 * 60)
    public void migrateMaterialVideo() {
        final RLock lock = lockService.getLock(QianchuanMaterialVideoMigrationTask.class.getSimpleName());
        if (!lock.tryLock()) {
            log.info("迁移千川素材视频品牌拼音数据锁已存在，跳过迁移");
            return;
        }
        try {
            log.info("开始迁移千川素材视频品牌拼音数据");
            final List<QianchuanMaterialVideo> updatedVideos = new ArrayList<>();
            materialVideoMapper.selectList(
                    new LambdaQueryWrapper<QianchuanMaterialVideo>()
                            .isNotNull(QianchuanMaterialVideo::getBrand)
                            .isNull(QianchuanMaterialVideo::getBrandSpell),
                    context -> {
                        QianchuanMaterialVideo video = context.getResultObject();
                        video.setBrandSpell(PinyinUtils.toPinyin(video.getBrand()));
                        updatedVideos.add(video);

                        if (updatedVideos.size() >= 1000) {
                            materialVideoService.updateBatchById(updatedVideos);
                            updatedVideos.clear();
                        }
                    }
            );

            if (!updatedVideos.isEmpty()) {
                materialVideoService.updateBatchById(updatedVideos);
            }
        } finally {
            lock.unlock();
        }

    }

}
