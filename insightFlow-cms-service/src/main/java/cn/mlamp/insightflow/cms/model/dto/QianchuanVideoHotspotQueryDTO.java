
package cn.mlamp.insightflow.cms.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class QianchuanVideoHotspotQueryDTO {
    @Schema(description = "榜单类型(总榜/新兴榜)")
    private String rankingType;

    @Schema(description = "行业分类")
    private String industry;

    @Schema(description = "消耗区间")
    private String consumeRange;

    @Schema(description = "素材形式")
    private String materialForm;

    @Schema(description = "最小视频时长(秒)")
    private Integer durationMin;

    @Schema(description = "最大视频时长(秒)")
    private Integer durationMax;

    @Schema(description = "排序选项列表，每个选项包含排序字段和排序方式")
    private List<SortOption> sortOptions;

    // 保留旧字段以保持向后兼容
    @Schema(description = "排序字段(consume_range_weight/exposure/shares/comments/likes/clicks/rating)，已弃用，请使用sortOptions")
    @Deprecated
    private String sortField;

    @Schema(description = "排序方式(asc/desc)，已弃用，请使用sortOptions")
    @Deprecated
    private String sortOrder;

    @Schema(description = "类型 0,1,2,3,4 综合,品牌,产品,标题,口播文案", required = false)
    private String selectType;

    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "开始发布时间")
    private LocalDateTime publishTimeStart;

    @Schema(description = "结束发布时间")
    private LocalDateTime publishTimeEnd;

    @Schema(description = "页码(默认1)")
    private Integer pageNum = 1;

    @Schema(description = "每页数量(默认10)")
    private Integer pageSize = 10;

    @Schema(description = "用户ID")
    private Integer userId;

    @Schema(description = "租户ID")
    private Integer tenantId;
}
