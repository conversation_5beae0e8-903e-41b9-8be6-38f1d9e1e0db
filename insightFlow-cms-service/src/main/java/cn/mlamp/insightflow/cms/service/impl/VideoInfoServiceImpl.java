package cn.mlamp.insightflow.cms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.entity.*;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.mapper.VideoInfoMapper;
import cn.mlamp.insightflow.cms.model.query.VideoInfoResultDetailRequest;
import cn.mlamp.insightflow.cms.model.query.VideoInfoResultRequest;
import cn.mlamp.insightflow.cms.model.vo.*;
import cn.mlamp.insightflow.cms.service.*;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition2Handle;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.common.recycler.Recycler;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.util.*;

import static cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum.UPLOAD_VIDEO;

/**
 * @Author: husuper
 * @CreateTime: 2025-03-21
 */
@Service
public class VideoInfoServiceImpl  extends ServiceImpl<VideoInfoMapper, CmsVideoInfo> implements IVideoInfoService {

    @Autowired
    private VideoInfoMapper videoInfoMapper;

    @Autowired
    private IVideoResultService videoResultService;

    @Autowired
    private IVideoResultDetailService videoResultDetailService;

    @Autowired
    private CmsPullTaskDedupedDataService cmsPullTaskDedupedDataService;

    @Autowired
    private QianchuanMaterialVideoService qianchuanMaterialVideoService;

    @Autowired
    @Lazy
    private  CmsPullTaskDedupedDataService pullTaskDedupedDataService;


    @Resource
    @Lazy
    private FileService fileService;

    @Resource
    @Lazy
    private IUserService userService;

    @Override
    public VideoInfoVO getReuslt(VideoInfoResultRequest videoInfoDetailRequest) {
        VideoInfoVO videoInfoVO=new VideoInfoVO();

        boolean flag=false;
        if(StringUtils.isNotBlank(videoInfoDetailRequest.getEsId())){
            //填充原始数据
            CmsPullTaskDedupedData cmsPullTaskDedupedData = cmsPullTaskDedupedDataService.getOne(new LambdaQueryWrapper<CmsPullTaskDedupedData>().eq(CmsPullTaskDedupedData::getEsId,videoInfoDetailRequest.getEsId()));
            if(cmsPullTaskDedupedData!=null){
                VideoInfoVO.PullVideoInfoVO pullVideoInfoVO=new VideoInfoVO.PullVideoInfoVO();
                BeanUtils.copyProperties(cmsPullTaskDedupedData,pullVideoInfoVO);
                videoInfoVO.setPullVideoInfo(pullVideoInfoVO);

                if(SourceTypeEnum.UPLOAD.getCode()==cmsPullTaskDedupedData.getSourceType()
                        ||SourceTypeEnum.LINK.getCode()==cmsPullTaskDedupedData.getSourceType()){
                    videoInfoDetailRequest.setType(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode());
                }else if(SourceTypeEnum.ES.getCode()==cmsPullTaskDedupedData.getSourceType()){
                    videoInfoDetailRequest.setType(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoCode());
                }


            }
            flag=true;
        }


        CmsVideoInfo cmsVideoInfo = null;
        if (videoInfoDetailRequest.getId() != null){
           cmsVideoInfo= videoInfoMapper.selectById(videoInfoDetailRequest.getId());
        }else{
            cmsVideoInfo=this.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId,videoInfoDetailRequest.getEsId())
                    .eq(CmsVideoInfo::getType,videoInfoDetailRequest.getType()));
        }

        if (cmsVideoInfo!=null){
            BeanUtils.copyProperties(cmsVideoInfo,videoInfoVO);
        }else{
            if(flag){
                return videoInfoVO;
            }
            throw new BusinessException("没有查询到视频分析结果信息");
        }

        videoInfoVO.setVideoInfoReuslts(new ArrayList<>());

        List<CmsVideoResult> videoResults= videoResultService.list(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId,cmsVideoInfo.getId()));
        for (CmsVideoResult cmsVideoResult : videoResults){
            VideoInfoVO.VideoInfoReuslt videoInfoReuslt=new VideoInfoVO.VideoInfoReuslt();
            BeanUtils.copyProperties(cmsVideoResult,videoInfoReuslt);
            videoInfoReuslt.setVideoInfoReusltId(cmsVideoResult.getId());
            videoInfoVO.getVideoInfoReuslts().add(videoInfoReuslt);
        }

        if(!flag){
            //填充原始数据
            CmsPullTaskDedupedData cmsPullTaskDedupedData = cmsPullTaskDedupedDataService.getOne(new LambdaQueryWrapper<CmsPullTaskDedupedData>().eq(CmsPullTaskDedupedData::getEsId,cmsVideoInfo.getEsId()));
            if(cmsPullTaskDedupedData!=null){
                VideoInfoVO.PullVideoInfoVO pullVideoInfoVO=new VideoInfoVO.PullVideoInfoVO();
                BeanUtils.copyProperties(cmsPullTaskDedupedData,pullVideoInfoVO);
                videoInfoVO.setPullVideoInfo(pullVideoInfoVO);
            }
        }

        return videoInfoVO;
    }

    @Override
    public VideoInfoResultVO getReusltDetail(VideoInfoResultDetailRequest videoInfoResultDetailRequest) {
        CmsVideoResult cmsVideoResult =videoResultService.getById(videoInfoResultDetailRequest.getVideoInfoReusltId());
        if(cmsVideoResult==null){
            throw new BusinessException("没有查询到视频结果详情信息");
        }
        VideoInfoResultVO videoInfoResultVO=new VideoInfoResultVO();
        BeanUtils.copyProperties(cmsVideoResult,videoInfoResultVO);
        videoInfoResultVO.setVideoInfoReusltId(cmsVideoResult.getId());

        videoInfoResultVO.setVideoInfoReusltDetails(new ArrayList<>());

        List<CmsVideoResultDetail> videoResultDetails= videoResultDetailService.list(new LambdaQueryWrapper<CmsVideoResultDetail>().eq(CmsVideoResultDetail::getVideoResultId,cmsVideoResult.getId()));
        for (CmsVideoResultDetail cmsVideoResultDetail : videoResultDetails){
            VideoInfoResultVO.VideoInfoReusltDetail videoInfoReusltDetail=new VideoInfoResultVO.VideoInfoReusltDetail();
            BeanUtils.copyProperties(cmsVideoResultDetail,videoInfoReusltDetail);
            videoInfoResultVO.getVideoInfoReusltDetails().add(videoInfoReusltDetail);
            videoInfoReusltDetail.setVideoInfoReusltDetailId(cmsVideoResultDetail.getId());
        }

        return videoInfoResultVO;
    }

    @Override
    public void exportReusltDetail(Integer videoId, HttpServletResponse response) {
       List<CmsVideoResult> cmsVideoResults = videoResultService.list(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,videoId)
                .eq(CmsVideoResult::getType, VideoResultTypeEnum.scene_split.getCode())
                .eq(BaseEntity::getIsDeleted,0));
        List<VideoInfoSceneSplitExcelVO> dataListMap = new ArrayList<>();

        for (CmsVideoResult cmsVideoResult : cmsVideoResults){
            dataListMap.add(JSONObject.parseObject(cmsVideoResult.getData(),VideoInfoSceneSplitExcelVO.class));
        }

        try {
            // 设置响应头信息
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Pragma", "No-cache");
            response.setHeader("Cache-Control", "No-cache");
            response.setDateHeader("Expires", 0);
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("分镜数据.xlsx", StandardCharsets.UTF_8));

            ServletOutputStream outputStream = response.getOutputStream();
            EasyExcel.write(outputStream, VideoInfoSceneSplitExcelVO.class)
                    .inMemory(true)
                    .autoCloseStream(true)
                    .sheet("分镜数据")
                    .doWrite(dataListMap);
        }catch (Exception e){
            log.error("导出失败",e);
            throw new BusinessException("导出失败");
        }
    }

    /**
     * 查询符合条件的视频数据
     *
     * @param type          视频类型
     * @param status        视频状态
     * @param threeGoldType 黄金五秒标签
     * @param industry      行业
     * @param limit         查询数量限制
     * @return 符合条件的视频数据列表
     */
    @Override
    public List<CmsVideoInfo> findVideosByCondition(Integer type, Integer status, String threeGoldType, String industry,
            Integer limit) {
        // 构建查询条件
        LambdaQueryWrapper<CmsVideoInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsVideoInfo::getType, type).eq(CmsVideoInfo::getStatus, status)
                .eq(CmsVideoInfo::getThreeGoldType, threeGoldType)
                .orderByDesc(CmsVideoInfo::getUpdateTime);

        // 如果行业不是"全部"，才添加行业筛选条件
        if (industry != null && !"全部".equals(industry)) {
            queryWrapper.eq(CmsVideoInfo::getIndustry, industry);
        }

        // 添加数量限制
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }

        // 执行查询
        return videoInfoMapper.selectList(queryWrapper);
    }

    @Override
    public QianchuanVideoInfoVO getQianchuanReuslt(VideoInfoResultRequest videoInfoDetailRequest) {
        CmsVideoInfo cmsVideoInfo=null;
        if(videoInfoDetailRequest.getId()!=null){
            cmsVideoInfo = videoInfoMapper.selectById(videoInfoDetailRequest.getId());
            if(cmsVideoInfo==null){
                throw new BusinessException("根据id没有查询到视频信息");
            }
            videoInfoDetailRequest.setEsId(cmsVideoInfo.getEsId());
            videoInfoDetailRequest.setType(cmsVideoInfo.getType());
        }


        if(StringUtils.isBlank(videoInfoDetailRequest.getEsId())){
            throw new BusinessException("视频ID不能为空");
        }
        if(videoInfoDetailRequest.getType()==null){
            throw new BusinessException("type不能为空");
        }

        QianchuanVideoInfoVO qianchuanVideoInfoVO=new QianchuanVideoInfoVO();
        QianchuanVideoInfoVO.VideoSummary videoSummary=new QianchuanVideoInfoVO.VideoSummary();


        if(videoInfoDetailRequest.getType()==1 || videoInfoDetailRequest.getType()==4){
            CmsPullTaskDedupedData cmsPullTaskDedupedData = cmsPullTaskDedupedDataService.getOne(new LambdaQueryWrapper<CmsPullTaskDedupedData>().eq(CmsPullTaskDedupedData::getEsId,videoInfoDetailRequest.getEsId()));
            qianchuanVideoInfoVO.setCmsPullTaskDedupedData(cmsPullTaskDedupedData);
            cmsVideoInfo =  videoInfoMapper.selectOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId,videoInfoDetailRequest.getEsId()).eq(CmsVideoInfo::getType,videoInfoDetailRequest.getType()));
        }else if(videoInfoDetailRequest.getType()==5){
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId,videoInfoDetailRequest.getEsId()));
            if(qianchuanMaterialVideo==null){
                throw new BusinessException("esId不存在");
            }
            qianchuanVideoInfoVO.setQianchuanMaterialVideo(qianchuanMaterialVideo);
            cmsVideoInfo =  videoInfoMapper.selectOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId,videoInfoDetailRequest.getEsId()).eq(CmsVideoInfo::getType,videoInfoDetailRequest.getType()));
            videoSummary.setBrand(qianchuanMaterialVideo.getBrand());
        }

        if(cmsVideoInfo==null){
            throw new BusinessException("esId不存在");
        }

        if(cmsVideoInfo.getTenantId()!=null){
            //校验用户是否有权限
            if(!userService.checkUserTenant(cmsVideoInfo.getTenantId())){
                throw new BusinessException("没有权限");
            }
        }

        qianchuanVideoInfoVO.setId(cmsVideoInfo.getId());
        qianchuanVideoInfoVO.setType(cmsVideoInfo.getType());
        qianchuanVideoInfoVO.setEsId(cmsVideoInfo.getEsId());
        qianchuanVideoInfoVO.setStatus(cmsVideoInfo.getStatus());
        qianchuanVideoInfoVO.setUserId(cmsVideoInfo.getUserId());
        qianchuanVideoInfoVO.setThreeGoldType(cmsVideoInfo.getThreeGoldType());
        qianchuanVideoInfoVO.setIndustry(cmsVideoInfo.getIndustry());





        videoSummary.setStartGold3s(qianchuanVideoInfoVO.getThreeGoldType());
        videoSummary.setOurIndustry(cmsVideoInfo.getIndustry());


        List<Map<String,Object>> sceneDecodings=new ArrayList<>();

        List<CmsVideoResult> videoResultList = videoResultService.list(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId,cmsVideoInfo.getId()).ne(CmsVideoResult::getType,VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode()).orderByAsc(CmsVideoResult::getType).orderByAsc(CmsVideoResult::getIndex));

        for (CmsVideoResult cmsVideoResult : videoResultList) {
            if(cmsVideoResult.getType()==VideoResultTypeEnum.ALL_ASR.getCode()){
                VideoRecognition2Handle.ASR asr = JSONObject.parseObject(cmsVideoResult.getData(), VideoRecognition2Handle.ASR.class);
                qianchuanVideoInfoVO.setAsr(asr.getText());
            }
            if(cmsVideoResult.getType()==VideoResultTypeEnum.INDUSTRY_DECODING2.getCode()){
                Map<String, Object> industryDecoding = JSONObject.parseObject(cmsVideoResult.getData(), Map.class);
                videoSummary.setProductName(joinWithSemicolon(String.valueOf(industryDecoding.get("产品名称"))));
                videoSummary.setCellingPoint(joinWithSemicolon(String.valueOf(industryDecoding.get("卖点"))));
                videoSummary.setAimingTribe(joinWithSemicolon(String.valueOf(industryDecoding.get("受众人群"))));
                videoSummary.setVideoSummary(String.valueOf(industryDecoding.get("视频总结")));
//                videoSummary.setStartGold3s(joinWithSemicolon(String.valueOf(industryDecoding.get("开篇黄金3s类型"))));
                videoSummary.setDuringSellingScene(industryDecoding.get("中段卖点场景")!=null?String.valueOf(industryDecoding.get("中段卖点场景")):null);
                videoSummary.setEndSellingDialogue(industryDecoding.get("逼单话术")!=null?String.valueOf(industryDecoding.get("逼单话术")):null);
                videoSummary.setMaterialForm(industryDecoding.get("视频形式")!=null?String.valueOf(industryDecoding.get("视频形式")):null);
            }
            if(cmsVideoResult.getType()==VideoResultTypeEnum.SCENE_SPLIT2.getCode()){
                Map<String,Object> sceneDecoding = JSONObject.parseObject(cmsVideoResult.getData(), Map.class);
                sceneDecoding.put("index",cmsVideoResult.getIndex());
                sceneDecoding.put("videoInfoReusltId",cmsVideoResult.getId());
                sceneDecodings.add(sceneDecoding);
            }
            if (cmsVideoResult.getType()==VideoResultTypeEnum.ASR5.getCode()){
                ASR5 asr5 = JSONObject.parseObject(cmsVideoResult.getData(), ASR5.class);
                videoSummary.setDialogueRoutine(asr5.getAsr5());
            }

        }

        Integer userId= UserContext.getUserId();
        Integer tenantId=UserContext.getTenantId();

        //替换分镜的用户版本
        List<CmsVideoResult> userVideoResults = videoResultService.list(new LambdaQueryWrapper<CmsVideoResult>()
                .eq(CmsVideoResult::getVideoId,cmsVideoInfo.getId())
                .eq(CmsVideoResult::getType,VideoResultTypeEnum.USER_SCENE_SPLIT2.getCode())
                .eq(CmsVideoResult::getUserId,userId)
                .eq(CmsVideoResult::getTenantId,tenantId));

        for (CmsVideoResult userVideoResult : userVideoResults){
            //替换用户的分镜数据
            for (Map<String,Object> sceneDecoding : sceneDecodings) {
                if(sceneDecoding.get("index").equals(userVideoResult.getIndex())){
                    Map<String,Object> userSceneDecoding=JSONObject.parseObject(userVideoResult.getData(), Map.class);
                    for (String key:userSceneDecoding.keySet()){
                        sceneDecoding.put(key,userSceneDecoding.get(key));
                    }
                    sceneDecoding.put("index",userVideoResult.getIndex());
                    sceneDecoding.put("videoInfoReusltId",userVideoResult.getId());
                    break;
                }
            }
        }

        for(Map<String,Object> sceneDecoding : sceneDecodings){
            if(sceneDecoding.get("sceneDecodingSegmentPicOssId")!=null){
                sceneDecoding.put("sceneDecodingSegmentPicUrl",fileService.getPicDownloadSignatureUrl(sceneDecoding.get("sceneDecodingSegmentPicOssId").toString()));
            }
            if(sceneDecoding.get("sceneDecodingOssId")!=null){
                sceneDecoding.put("sceneDecodingPicUrl",fileService.getPicDownloadSignatureUrl(sceneDecoding.get("sceneDecodingPicOssId").toString()));
            }
        }


        qianchuanVideoInfoVO.setVideoSummary(videoSummary);
        qianchuanVideoInfoVO.setSceneDecodings(sceneDecodings);
        return qianchuanVideoInfoVO;
    }


    public String joinWithSemicolon(String data) {
        if (data == null||StringUtils.isBlank(data)) {
            return "";
        }
        List<String> items = JSONObject.parseArray(data, String.class);
        if (items == null || items.isEmpty()) {
            return "";
        }
        return String.join("；", items);
    }

    @Data
    @AllArgsConstructor
    public static class ASR5 {
        private String asr5;
    }

    @Override
    public CmsVideoInfo findOneByEsId(String esId){
        return videoInfoMapper.selectOne(new LambdaQueryWrapper<CmsVideoInfo>()
                .eq(CmsVideoInfo::getEsId,esId)
                .orderByDesc(CmsVideoInfo::getCreateTime)
                .last("LIMIT 1")
        );
    }

    @Override
    public void updateStatusByDocId(Integer docId, VideoInfoStatusEnum videoInfoStatusEnum, String errorMsg) {
        videoInfoMapper.update(new LambdaUpdateWrapper<CmsVideoInfo>()
                .set(CmsVideoInfo::getStatus, videoInfoStatusEnum.getCode())
                .set(StrUtil.isNotBlank(errorMsg), CmsVideoInfo::getErrorMessage, errorMsg)
                .eq(CmsVideoInfo::getSourceFileId, docId)
        );
    }

    /**
     * 获取当天在处理中的 CmsVideoInfo 数据
     *
     * @return 处理中的 CmsVideoInfo 列表
     */
    public List<CmsVideoInfo> getProcessingVideoInfosByToday() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 构建查询条件
        LambdaQueryWrapper<CmsVideoInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsVideoInfo::getStatus, VideoInfoStatusEnum.PROCESSING.getCode())
                .ge(CmsVideoInfo::getCreateTime, currentDate.atStartOfDay())
                .lt(CmsVideoInfo::getCreateTime, currentDate.plusDays(1).atStartOfDay());

        // 执行查询
        return this.list(queryWrapper);
    }

    @Override
    public Page<VideoTaskListInfoVO> pageUserVideoTask(Integer current, Integer pageSize, Integer tenantId) {
        Page<VideoTaskListInfoVO> page = new Page<>(current, pageSize);
        return baseMapper.pageVideoInfo(page, UPLOAD_VIDEO.getVideoCode(), tenantId, null);
    }


    public void handleErrorData() {
       List<CmsVideoInfo> cmsVideoInfos =  videoInfoMapper.selectQianchuanAndVideoInfo();
       for (CmsVideoInfo cmsVideoInfo : cmsVideoInfos){
            //把千川表数据更新为成功的
           try {
               qianchuanMaterialVideoService.update(new LambdaUpdateWrapper<QianchuanMaterialVideo>()
                       .set(QianchuanMaterialVideo::getAnalysisStatus, 2)
                       .eq(QianchuanMaterialVideo::getVideoId, cmsVideoInfo.getEsId())
                       .eq(QianchuanMaterialVideo::getIsDeleted, 0));
           }catch (Exception e){
               log.error("更新千川表数据失败",e);
           }
       }
    }


    //开启一个新的事物
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateVideoInfoStatusFailed(Integer id,String esId, String message) {
        if (message != null && message.length() > 800) {
            message = message.substring(0, 800);
        }
        if(StringUtils.isNotBlank(esId)){
            QianchuanMaterialVideo qianchuanMaterialVideo = qianchuanMaterialVideoService.getOne(new LambdaQueryWrapper<QianchuanMaterialVideo>().eq(QianchuanMaterialVideo::getVideoId, esId));
            qianchuanMaterialVideo.setAnalysisStatus(3);
            qianchuanMaterialVideo.setUpdateTime(new Date());
            qianchuanMaterialVideoService.updateById(qianchuanMaterialVideo);
        }

        if (id == null) {
            return;
        }
        CmsVideoInfo videoInfo = this.getById(id);
        if (videoInfo == null) {
            return;
        }
        videoInfo.setStatus(VideoInfoStatusEnum.ERROR.getCode());
        videoInfo.setErrorMessage(message);
        this.updateById(videoInfo);


    }


    //根据id更新视频信息状态为失败的
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateESVideoInfoStatusFailed(Integer id, String message) {
        //如果message不为空且超过了800则取前800个字符
        if (message != null && message.length() > 800) {
            message = message.substring(0, 800);
        }

        if (id == null) {
            return;
        }
        CmsVideoInfo videoInfo = this.getById(id);
        if (videoInfo == null) {
            return;
        }
        videoInfo.setStatus(VideoInfoStatusEnum.ERROR.getCode());
        videoInfo.setErrorMessage(message);
        videoInfo.setUpdateTime(new Date());
        this.updateById(videoInfo);

        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsPullTaskDedupedData::getEsId, videoInfo.getEsId());
        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
        dedupedData.setAnalysisStatus(AnalysisStatusEnum.ERROR.getCode());
        dedupedData.setUpdateTime(new Date());
        pullTaskDedupedDataService.updateById(dedupedData);
    }

}
