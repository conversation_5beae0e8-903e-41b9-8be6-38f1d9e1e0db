package cn.mlamp.insightflow.cms.util;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

@Slf4j
public class PinyinUtils {

    // 判断是否为汉字
    private static boolean isChineseChar(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    // 将汉字转为拼音, 汉字以外字符不变
    public static String toPinyin(String str) {
        final HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        format.setVCharType(HanyuPinyinVCharType.WITH_V);

        final StringBuilder sb = new StringBuilder();
        final char[] chars = str.toCharArray();
        for (char c : chars) {
            if (isChineseChar(c)) {
                try {
                    final String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, format);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        sb.append(pinyinArray[0]);
                    }
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    log.error(e.getMessage(), e);
                    sb.append(c);
                }
            } else {
                // 非汉字字符转小写
                sb.append(Character.toLowerCase(c));
            }
        }
        return sb.toString();
    }
    
}
