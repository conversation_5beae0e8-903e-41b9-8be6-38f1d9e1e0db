package cn.mlamp.insightflow.cms.model.dto.dam;

import java.util.List;

import cn.hutool.core.util.StrUtil;
import cn.mlamp.insightflow.cms.enums.ErrorCode;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * DAM标签DTO
 */
@Data
@Schema(description = "DAM标签DTO")
public class DamTagDTO {

    @NotBlank(message = "标签名称不能为空")
    @Size(max = 40, message = "标签名称不能超过40个字符")
    @Schema(description = "标签名称",
            requiredMode = RequiredMode.REQUIRED)
    private String name;

    @NotBlank(message = "标签描述不能为空")
    @Size(max = 255, message = "标签描述不能超过255个字符")
    @Schema(description = "标签描述",
            requiredMode = RequiredMode.REQUIRED)
    private String description;

    @NotBlank(message = "标签示例不能为空")
    @Size(max = 255, message = "标签示例不能超过255个字符")
    @Schema(description = "标签示例",
            requiredMode = RequiredMode.REQUIRED)
    private String example;

    public void validate() {
        // 校验标签类型
        if (StrUtil.isBlank(name)) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "标签名称不能为空");
        }
        if (StrUtil.isBlank(description)) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "标签描述不能为空");
        }
        if (StrUtil.isBlank(example)) {
            throw new BusinessException(ErrorCode.PARAM_ERROR, "标签示例不能为空");
        }
    }

} 