package cn.mlamp.insightflow.cms.model.dto;

import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DifyProductSummaryResponseDTO {
    @Schema(description = "品牌", required = true)
    private String brand;

    @Schema(description = "产品名称", required = true)
    private String productName;

    @Schema(description = "卖点", required = true)
    private String sellingPoint;

    public static DifyProductSummaryResponseDTO buildByText(String text) {
        if (StrUtil.isBlank(text)) {
            return null;
        }
        var dto = new DifyProductSummaryResponseDTO();
        String[] lines = text.split("\\r?\\n");
        for (String line : lines) {
            if (line.contains("品牌")) {
                var brand = line.substring(line.indexOf("：") + 1).trim();
                dto.setBrand(brand);
            } else if (line.contains("商品名")) {
                var productName = line.substring(line.indexOf("：") + 1).trim();
                dto.setProductName(productName);
            } else if (line.contains("卖点")) {
                var sellingPoint = line.substring(line.indexOf("：") + 1).trim();
                dto.setSellingPoint(sellingPoint);
            }
        }
        return dto;
    }
    public static DifyProductSummaryResponseDTO buildByText1(String text) {
        if (StrUtil.isBlank(text)) {
            return null;
        }
        var dto = new DifyProductSummaryResponseDTO();
        String[] lines = text.split("\\r?\\n");
        String pre = "";
        var sellingPoint = "";
        for (String line : lines) {
            if (!line.contains("商品品牌") && line.equals("") ){
                continue;
            }
            if (line.contains("商品品牌")) {
                pre = "商品品牌";
                var brand = line.substring(line.indexOf("：") + 1).trim();
                dto.setBrand(brand.replace("{", "").replace("}", ""));
            } else if (line.contains("商品名称")) {
                pre = "商品名称";
                var productName = line.substring(line.indexOf("：") + 1).trim();
                dto.setProductName(productName.replace("{", "").replace("}", ""));
            } else if (line.contains("商品卖点")) {
                pre = "商品卖点";
                sellingPoint = line.substring(line.indexOf("：") + 1).trim();
            }  else if (pre.equals("商品卖点")){
                sellingPoint += line.substring(line.indexOf("：") + 1).trim();
            }
        }
        dto.setSellingPoint(sellingPoint.replace("{", "").replace("}", "").replace("-",""));
        return dto;
    }

 /*  public static void main(String[] args) {
        String productText =
                "根据提供的文档内容，以下是提取出的商品信息：\n\n- 商品品牌：欧莱雅\n- 商品名称：洗面奶6666\n- 商品卖点：\n  - 深层清洁：有效去除脸部污垢和杂质，保持肌肤清爽洁净。\n  - 温和配方：适合各种肤质，特别是敏感肌肤使用。\n  - 保湿滋润：洗后不紧绷，肌肤倍感水润柔滑。\n  - 专业护肤：源于专业护肤品牌欧莱雅的创新技术，提升护肤体验。\n\n这些卖点可以在电商平台上用于吸引消费者关注和购买。";
        String productText1 = "商品品牌：欧莱雅  \n商品名称：洗面奶6666  \n商品卖点：  \n1. 深层清洁：有效去除肌肤表面的污垢和多余油脂，保持清爽洁净。  \n2. 温和配方：适合各种肤质，特别是敏感肌肤，使用后肌肤不紧绷。  \n3. 保湿滋润：洗后肌肤柔软不干燥，提供长效保湿效果。  \n4. 皮肤健康：含有丰富的护肤成分，改善肤质，提升肌肤自然光泽。";
        System.out.println(buildByText1(productText1));
    }*/
}
