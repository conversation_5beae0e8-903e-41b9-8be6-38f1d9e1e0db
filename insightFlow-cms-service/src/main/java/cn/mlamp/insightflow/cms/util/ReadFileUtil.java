package cn.mlamp.insightflow.cms.util;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.web.multipart.MultipartFile;
import java.io.*;
import java.nio.charset.StandardCharsets;

public class ReadFileUtil {

    public static String readTextFromFile(MultipartFile file) throws IOException {
        String filename = file.getOriginalFilename();
        if (filename == null) throw new IOException("文件名为空");

        String extension = getFileExtension(filename).toLowerCase();

        return switch (extension) {
            case "txt", "md" -> readText(file);
            case "csv" -> readCSV(file.getInputStream());
            case "pdf" -> readPDF(file.getInputStream());
            case "xls", "xlsx" -> readExcel(file.getInputStream());
            case "doc" -> readDoc(file);
            case "docx" -> readDocx(file);
            default -> throw new IOException("不支持的文件格式: " + extension);
        };
    }

    private static String getFileExtension(String filename) {
        int lastDot = filename.lastIndexOf('.');
        return (lastDot == -1) ? "" : filename.substring(lastDot + 1);
    }

    /**
     * 读取 MultipartFile 类型的 txt 文件内容
     *
     * @param file 前端上传的文件
     * @return 文件内容字符串
     * @throws IOException 如果读取失败
     */
    /**
     * 自动识别文件编码并读取文本内容
     */
    public static String readText(MultipartFile file) throws IOException {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("文件不能为空");
        }

        // 第一步：检测文件编码
        String charset = detectCharset(file);
        if (charset == null) {
            // 默认使用 UTF-8，如果检测失败
            charset = "UTF-8";
        }

        // 第二步：按检测到的编码读取内容
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), charset))) {

            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append(System.lineSeparator());
            }
        }

        return content.toString();
    }

    /**
     * 使用 juniversalchardet 检测文件编码
     */
    private static String detectCharset(MultipartFile file) throws IOException {
        byte[] buf = new byte[4096];
        UniversalDetector detector = new UniversalDetector(null);

        try (InputStream is = file.getInputStream()) {
            int nread;
            while ((nread = is.read(buf)) > 0 && !detector.isDone()) {
                detector.handleData(buf, 0, nread);
            }
            detector.dataEnd();
            return detector.getDetectedCharset();
        }
    }


    private static String readCSV(InputStream is) throws IOException {
        StringBuilder result = new StringBuilder();
        CSVParser parser = CSVFormat.DEFAULT.parse(new InputStreamReader(is, StandardCharsets.UTF_8));
        for (CSVRecord record : parser) {
            for (String cell : record) {
                result.append(cell).append(" ");
            }
            result.append("\n");
        }
        return result.toString();
    }

    private static String readPDF(InputStream is) throws IOException {
        try (PDDocument document = PDDocument.load(is)) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    private static String readExcel(InputStream is) throws IOException {
        Workbook workbook = new XSSFWorkbook(is); // 支持 .xlsx
        StringBuilder result = new StringBuilder();
        for (Sheet sheet : workbook) {
            for (Row row : sheet) {
                for (Cell cell : row) {
                    result.append(getCellValue(cell)).append(" ");
                }
                result.append("\n");
            }
        }
        workbook.close();
        return result.toString();
    }

    private static String getCellValue(Cell cell) {
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> String.valueOf(cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> cell.getCellFormula();
            default -> "";
        };
    }


    /**
     * 读取 .doc 文件内容
     */
    public static String readDoc(MultipartFile file) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             HWPFDocument document = new HWPFDocument(inputStream);
             WordExtractor extractor = new WordExtractor(document)) {

            return extractor.getText();
        }
    }


    /**
     * 读取 .docx 文件中的文本内容
     */
    public static String readDocx(MultipartFile file) throws IOException {

        try (InputStream inputStream = file.getInputStream();
             OPCPackage opcPackage = OPCPackage.open(inputStream);
             XWPFDocument document = new XWPFDocument(opcPackage)) {

            StringBuilder text = new StringBuilder();

            // 遍历段落
            document.getParagraphs().forEach(p -> text.append(p.getText()).append("\n"));

            // 如果有表格，也可以读取
            document.getTables().forEach(table -> {
                text.append("表格内容：\n");
                table.getRows().forEach(row -> {
                    row.getTableCells().forEach(cell -> {
                        text.append(cell.getText()).append(" | ");
                    });
                    text.append("\n");
                });
            });

            return text.toString();
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
    }


}