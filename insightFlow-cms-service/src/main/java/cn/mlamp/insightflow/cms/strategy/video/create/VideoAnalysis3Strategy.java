package cn.mlamp.insightflow.cms.strategy.video.create;

import cn.mlamp.insightflow.cms.common.file.IS3FlowService;
import cn.mlamp.insightflow.cms.config.AnalysisVideoConfig;
import cn.mlamp.insightflow.cms.entity.*;
import cn.mlamp.insightflow.cms.enums.*;
import cn.mlamp.insightflow.cms.exception.BusinessException;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoCreateRequest;
import cn.mlamp.insightflow.cms.model.query.AnalysisVideoQueryRequest;
import cn.mlamp.insightflow.cms.model.query.AsyncResultRequest;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoCreateVO;
import cn.mlamp.insightflow.cms.model.vo.AnalysisVideoResultVO;
import cn.mlamp.insightflow.cms.service.*;
import cn.mlamp.insightflow.cms.strategy.handle.VideoRecognition3Handle;
import cn.mlamp.insightflow.cms.util.FileDownloadUtil;
import cn.mlamp.insightflow.cms.util.FilePathBuilder;
import cn.mlamp.insightflow.cms.util.ObservationIdUtil;
import cn.mlamp.insightflow.cms.util.VideoUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;


/**
 * @Author: husuper
 * @CreateTime: 2025-02-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VideoAnalysis3Strategy implements ProcessAnalysisVideoStrategyInterface {

    private final CmsPullTaskDedupedDataService pullTaskDedupedDataService;

    private final VideoRecognition3Handle videoRecognition3Handle;

    private final IVideoInfoService videoInfoService;

    private final IVideoResultService videoResultService;

    private final IVideoAsrService videoAsrService;

    private final FileService fileService;

    private final QianchuanVideoStrategy qianchuanVideoStrategy;

    private final CmsAsyncTaskService cmsAsyncTaskService;

    @Resource(name = "cmsS3FlowService")
    private IS3FlowService cmsS3FlowService;

    private final AnalysisVideoConfig analysisVideoConfig;

    @Autowired
    private final TokenUseDetailService tokenUseDetailService;

    @Resource(name = "analysisThreadExecutor")
    private final ExecutorService analysisThreadExecutor;

    private static final String TASK_QUEUE = "customer";


    @PostConstruct
    public void init() {
        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType(), this);
        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType(), this);
    }


    @Override
    @Transactional
    public AnalysisVideoCreateVO process(AnalysisVideoCreateRequest request) {
        Integer id = null;
        try {
            String esId = request.getEsId();
            if (StringUtils.isBlank(esId)) {
                throw new BusinessException("esId不能为空");
            }
            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId);
            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
            if (dedupedData == null) {
                throw new BusinessException("esId不存在");
            }
            if (dedupedData.getDownloadStatus() != DownloadStatusEnum.SUCCESS.getCode()) {
                throw new BusinessException("视频未下载完成");
            }


            //调用算法
            VideoRecognition3Handle.RecognitionArg recognitionArg = new VideoRecognition3Handle.RecognitionArg();
            if (StringUtils.isBlank(request.getVideoDownloadUrl())) {
                recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getDatePublishedAt(), dedupedData.getEsId()));
                if (dedupedData.getSourceType() == 3) { //链接上传,取createTime
                    recognitionArg.setVideo_url(VideoUtil.getVideoUrl(dedupedData.getCreateTime(), dedupedData.getEsId()));
                }
            } else {
                recognitionArg.setVideo_url(request.getVideoDownloadUrl());
            }

            if (dedupedData.getSourceType() == 2) {
                dedupedData.setVideoOssId(dedupedData.getKwVideoUrl());
            } else if (dedupedData.getSourceType() == 3) {
                dedupedData.setVideoOssId(VideoUtil.getESVideoOSSId(dedupedData.getCreateTime(), dedupedData.getEsId()));
            } else if (dedupedData.getSourceType() == 1) {
                dedupedData.setVideoOssId(VideoUtil.getESVideoOSSId(dedupedData.getDatePublishedAt(), dedupedData.getEsId()));
            }

            //是否是开放地址
            boolean isOpen = true;
            if (dedupedData.getSourceType() == 1) {
                isOpen = true;
            }

            //下载视频
            String videoUrl = recognitionArg.getVideo_url();
            String fileName = dedupedData.getEsId() + ".mp4";
            String localFilePath = FileDownloadUtil.getPath(fileName);
            FileDownloadUtil.downloadFile2(videoUrl, localFilePath);
            //ASR抽离
            String audiofileName = dedupedData.getEsId() + ".wav";
            String audiolocalFilePath = FileDownloadUtil.getPath(audiofileName);
            VideoUtil.convertVideoToAudio(localFilePath, audiolocalFilePath);
            VideoRecognition3Handle.ASR asr = videoRecognition3Handle.asrGpuService(audiolocalFilePath, request.getEsId());
            FileDownloadUtil.deleteFile(audiolocalFilePath);

            //压缩视频
            String compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
            VideoUtil.compressVideo(localFilePath, compressMp4Path);
            //上传压缩视频
            String compressMp4OSSId = FilePathBuilder.getUserOpenVideoDecodeOssPath(esId, "compress_" + esId + ".mp4");
            uploadVideo(compressMp4OSSId, compressMp4Path);


            //切出视频头图
            String headPicFilePath = FileDownloadUtil.getPath("head_pic_" + esId + ".jpg");
            VideoUtil.cutImage(localFilePath, headPicFilePath, 5);
            String headPicOSSId = FilePathBuilder.getVideoDecodeOssPath(esId, "head_pic_" + esId + ".jpg", isOpen);
            uploadVideo(headPicOSSId, headPicFilePath);
            dedupedData.setVideoHeadPicOssId(headPicOSSId);
            FileDownloadUtil.deleteFile(headPicFilePath);
            recognitionArg.setVideo_url(analysisVideoConfig.getDomain() + "/" + compressMp4OSSId);
            recognitionArg.setTitle(dedupedData.getTextTitle());
            recognitionArg.setContent(dedupedData.getTextContent());
//            recognitionArg.setVideo_asr(asr);
            recognitionArg.setIndustry(IndustryEnum.getByCode(dedupedData.getKwKbIndustry()).getDescription());
            String observationId = null;
            if (AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType().equals(request.getTypeName())) {
                observationId = ObservationIdUtil.getObservationId(TokenTaskTypeEnum.UPLOAD_TASK);
            } else {
                observationId = ObservationIdUtil.getObservationId(AnalysisVideoTypeEnum.VIDEO_ANALYSIS);
            }
            if (dedupedData.getSourceType() == 2) {
                recognitionArg.setContent(null);
                recognitionArg.setTitle(null);
            }
            recognitionArg.setTask_queue(TASK_QUEUE);

            VideoRecognition3Handle.ProcessVideo processVideo = videoRecognition3Handle.processVideo(recognitionArg, observationId);

            String db_unique_id = processVideo.getData().getDb_unique_id();

            //保存异步任务表数据
            cmsAsyncTaskService.save(db_unique_id, AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode(), 1, dedupedData.getEsId(), "async_video_flow_v2");


            dedupedData.setDbUniqueId(db_unique_id);
            dedupedData.setAnalysisStatus(AnalysisStatusEnum.PROCESSING.getCode());
            dedupedData.setUpdateTime(new Date());
            pullTaskDedupedDataService.updateById(dedupedData);

            AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
            log.info("视频整体分析开始任务");
            CmsVideoInfo videoInfo = new CmsVideoInfo();
            if (type == AnalysisVideoTypeEnum.VIDEO_ANALYSIS) {
                videoInfo.setEsId(esId);
                videoInfo.setType(type.getVideoCode());
                videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
                Arg arg = new Arg();
                arg.setDbUniqueId(db_unique_id);
                arg.setCompressMp4OSSId(compressMp4OSSId);
                videoInfo.setArg(JSONObject.toJSONString(arg));
                videoInfoService.save(videoInfo);
            }
            if (type == AnalysisVideoTypeEnum.UPLOAD_VIDEO && request.getVideoInfoId() != null) { // 上传视频已经创建过videInfo,并且关联了document
                videoInfo = videoInfoService.getById(request.getVideoInfoId());
                Arg arg = new Arg();
                arg.setDbUniqueId(db_unique_id);
                arg.setObservationId(observationId);
                arg.setCompressMp4OSSId(compressMp4OSSId);
                videoInfo.setArg(JSONObject.toJSONString(arg));
                videoInfo.setStatus(VideoInfoStatusEnum.PROCESSING.getCode());
                videoInfoService.updateById(videoInfo);
            }
            id = videoInfo.getId();

            saveASR(videoInfo, asr);

            AnalysisVideoCreateVO analysisVideoCreateVO = new AnalysisVideoCreateVO();
            analysisVideoCreateVO.setId(videoInfo.getId());


            return analysisVideoCreateVO;

        } catch (Exception e) {
            log.error("视频分析失败", e);
            videoInfoService.updateESVideoInfoStatusFailed(id, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }

    private void saveASR(CmsVideoInfo videoInfo,  VideoRecognition3Handle.ASR asr){
        //保存结果数据
        CmsVideoResult videoResult = new CmsVideoResult();
        videoResult.setVideoId(videoInfo.getId());
        videoResult.setType(VideoResultTypeEnum.ALL_ASR.getCode());
        videoResult.setData(JSONObject.toJSONString(asr));
        videoResultService.save(videoResult);

        //保存结果数据
        CmsVideoResult videoResult2 = new CmsVideoResult();
        videoResult2.setVideoId(videoInfo.getId());
        videoResult2.setType(VideoResultTypeEnum.ASR5.getCode());
        videoResult2.setData(JSONObject.toJSONString(getFirst5SecondsASR(asr)));
        videoResultService.save(videoResult2);
    }



    @Data
    public static class Arg {
        //视频分析任务Id
        private String dbUniqueId;

        private String observationId;

        private String compressMp4OSSId;
    }

    /**
     * 获取前5秒的ASR结果
     *
     * @param asr VideoRecognition2Handle.ASR对象
     * @return 拼接后的前5秒ASR文本
     */
    public QianchuanVideoStrategy.ASR5 getFirst5SecondsASR(VideoRecognition3Handle.ASR asr) {
        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
            return new QianchuanVideoStrategy.ASR5("");
        }

        StringBuilder result = new StringBuilder();
        for (VideoRecognition3Handle.Sentences sentence : asr.getSentences()) {
            if (sentence.getStart() < 3000) { // 保留start小于3000毫秒的句子
                result.append(sentence.getText()).append(" ");
            }
        }

        return new QianchuanVideoStrategy.ASR5(result.toString().trim()); // 去除末尾多余的空格
    }


    @Override
    @Transactional
    public AnalysisVideoResultVO queryResult(AnalysisVideoQueryRequest request) {
        String esId = request.getEsId();
        if (StringUtils.isBlank(esId)) {
            throw new BusinessException("esId不能为空");
        }
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId);
        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
        if (dedupedData == null) {
            throw new BusinessException("esId不存在");
        }
        AnalysisVideoTypeEnum type = AnalysisVideoTypeEnum.getByVideoTypeStr(request.getTypeName());
        if (type == null) {
            throw new BusinessException("getTypeName不存在");
        }
        CmsVideoInfo cmsVideoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, esId)
                .eq(CmsVideoInfo::getType, type.getVideoCode())
                .orderByDesc(CmsVideoInfo::getCreateTime)
                .last("limit 1"));
        if (cmsVideoInfo == null) {
            throw new BusinessException("没有查询到视频分析结果信息");
        }
        AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
        analysisVideoResultVO.setId(cmsVideoInfo.getId());
        analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
        return analysisVideoResultVO;
    }


    @Transactional
    public AnalysisVideoResultVO queryResult2(String response_body, CmsVideoInfo cmsVideoInfo) {
        Integer id = null;
//        String localMp4FilePath = null;
        String compressMp4Path = null;
        try {
            String esId = cmsVideoInfo.getEsId();
            if (StringUtils.isBlank(esId)) {
                throw new BusinessException("esId不能为空");
            }
            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, esId);
            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
            if (dedupedData == null) {
                throw new BusinessException("esId不存在");
            }


            id = cmsVideoInfo.getId();

            AnalysisVideoResultVO analysisVideoResultVO = new AnalysisVideoResultVO();
            analysisVideoResultVO.setId(cmsVideoInfo.getId());

            boolean isOpen = false;
            if (dedupedData.getSourceType() == 1) {
                isOpen = true;
            }

            if (cmsVideoInfo.getStatus() != VideoInfoStatusEnum.PROCESSING.getCode()) {
                analysisVideoResultVO.setStatus(cmsVideoInfo.getStatus());
                if (VideoInfoStatusEnum.ERROR.getCode() == cmsVideoInfo.getStatus()) {
                    dedupedData.setUpdateTime(new Date());
                    dedupedData.setAnalysisStatus(3);
                    pullTaskDedupedDataService.updateById(dedupedData);
                }
                if (VideoInfoStatusEnum.SUCCESS.getCode() == cmsVideoInfo.getStatus()) {
                    dedupedData.setUpdateTime(new Date());
                    dedupedData.setAnalysisStatus(2);
                    pullTaskDedupedDataService.updateById(dedupedData);
                }

                return analysisVideoResultVO;
            }

            Arg arg = JSONObject.parseObject(cmsVideoInfo.getArg(), Arg.class);



            //保存整体分析结果数据
            CmsVideoResult videoResult = new CmsVideoResult();
            videoResult.setVideoId(cmsVideoInfo.getId());
            videoResult.setType(VideoResultTypeEnum.INDUSTRY_DECODING2.getCode());
            videoResult.setData(response_body);
            videoResultService.save(videoResult);

            //整体分析结果
            Map<String, Object> industryDecoding = JSONObject.parseObject(response_body, Map.class);

            //取出清洗后的ASR
//            VideoRecognition3Handle.ASR asr2=JSONObject.parseObject(JSONObject.toJSONString(industryDecoding.get("clean_asr")),  VideoRecognition3Handle.ASR.class);
//            saveASR(cmsVideoInfo, asr2);

            //黄金3秒打标
            compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
            String fileName = esId + ".mp4";
//            localMp4FilePath = FileDownloadUtil.getPath(fileName);


            VideoRecognition3Handle.ImageDecodingRequest imageDecodingRequest = new VideoRecognition3Handle.ImageDecodingRequest();
            imageDecodingRequest.setImages(List.of(VideoUtil.cutImageOfBase64(compressMp4Path, 1000),
                    VideoUtil.cutImageOfBase64(compressMp4Path, 2000),
                    VideoUtil.cutImageOfBase64(compressMp4Path, 3000),
                    VideoUtil.cutImageOfBase64(compressMp4Path, 4000),
                    VideoUtil.cutImageOfBase64(compressMp4Path, 5000)
            ));
            imageDecodingRequest.setTask_queue(TASK_QUEUE);

            CmsVideoResult videoResult2 = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ASR5.getCode()));
            QianchuanVideoStrategy.ASR5 asr5 = JSONObject.toJavaObject(JSONObject.parseObject(videoResult2.getData()), QianchuanVideoStrategy.ASR5.class);
            imageDecodingRequest.setSentences(asr5.getAsr5());
            imageDecodingRequest.setTitle(dedupedData.getTextTitle());
            imageDecodingRequest.setContent(null);
            VideoRecognition3Handle.ImageDecodingResponse imageDecodingResponse = videoRecognition3Handle.imageDecoding3s(imageDecodingRequest, arg.getObservationId());
//            dedupedData.setHighlight(imageDecodingResponse.getData().getIs_highlight());

            //保存黄金3秒的打标
            cmsAsyncTaskService.save(imageDecodingResponse.getData().getDb_unique_id(), AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode(), 1, dedupedData.getEsId(), "async_image_decoding_3s");


            CmsVideoResult videoResult3 = new CmsVideoResult();
            videoResult3.setVideoId(cmsVideoInfo.getId());
            videoResult3.setType(VideoResultTypeEnum.GOLD_FIVE2.getCode());
            videoResult3.setData(JSONObject.toJSONString(imageDecodingResponse.getData()));
            videoResultService.save(videoResult3);


            // 6: 视频分割接口
            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, cmsVideoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ALL_ASR.getCode()));
            VideoRecognition3Handle.ASR asr = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.ASR.class);
            VideoRecognition3Handle.VideoSplitRequest videoSplitRequest = new VideoRecognition3Handle.VideoSplitRequest();
            videoSplitRequest.setVideo_url(fileService.getPicDownloadSignatureUrl(dedupedData.getVideoOssId()));
            videoSplitRequest.setVideo_asr(asr);
            videoSplitRequest.setTask_queue(TASK_QUEUE);
            VideoRecognition3Handle.VideoSplitResponse videoSplitResponse = videoRecognition3Handle.videoSplit(videoSplitRequest, arg.getObservationId());

            cmsAsyncTaskService.save(videoSplitResponse.getData().getDb_unique_id(), AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode(), 1, dedupedData.getEsId(), "async_video_split");


            //更新黄金3秒类型
//            cmsVideoInfo.setThreeGoldType(imageDecodingResponse.getData().getHighlight_type());
            cmsVideoInfo.setRating("0");
            cmsVideoInfo.setUpdateTime(new Date());
            videoInfoService.updateById(cmsVideoInfo);
            analysisVideoResultVO.setStatus(VideoInfoStatusEnum.SUCCESS.getCode());
            //更新千川表的状态
            dedupedData.setAnalysisStatus(2);
            dedupedData.setProductName(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("产品名称") + ""));
            dedupedData.setCellingPoint(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("卖点") + ""));
            dedupedData.setAimingTribe(qianchuanVideoStrategy.joinWithSemicolon(industryDecoding.get("受众人群") + ""));

            dedupedData.setRating(Float.parseFloat(cmsVideoInfo.getRating()));
            dedupedData.setUpdateTime(new Date());
            pullTaskDedupedDataService.updateById(dedupedData);

            return analysisVideoResultVO;
        } catch (Exception e) {
            log.error("视频分析失败", e);
            videoInfoService.updateESVideoInfoStatusFailed(id, e.getMessage());
            throw new BusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public void processAsyncTask(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        switch (cmsAsyncTask.getTaskType()) {
            case "async_video_flow_v2":
                processVideoFlowV2(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_video_split":
                processVideoSplit(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_scene_decoding":
                processSceneDecoding(cmsAsyncTask, asyncResultRequest);
                break;
            case "async_image_decoding_3s":
                processImageDecoding3s(cmsAsyncTask, asyncResultRequest);
                break;
            default:
                break;
        }
    }

    //异步黄金三秒
    public void processImageDecoding3s(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理黄金三秒打标任务: {}", cmsAsyncTask.getEsId());

        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            // 2. 解析异步结果
            Map<String, Object> data = (Map<String, Object>) asyncResultRequest.getData();

            // 3. 保存黄金三秒结果
            CmsVideoResult videoResult = new CmsVideoResult();
            videoResult.setVideoId(videoInfo.getId());
            videoResult.setType(VideoResultTypeEnum.GOLD_FIVE2.getCode());
            videoResult.setData(JSONObject.toJSONString(asyncResultRequest.getData()));
            videoResultService.save(videoResult);

            // 4. 更新视频信息中的黄金三秒类型字段
            videoInfo.setThreeGoldType((String) data.get("highlight_type"));
            videoInfoService.updateById(videoInfo);

            //更新异步任务表的数据
            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);


            log.info("黄金三秒打标任务完成: {}", cmsAsyncTask.getEsId());

//            handleStatus(videoInfo,cmsAsyncTask);
        } catch (Exception e) {
            log.error("黄金三秒打标任务失败", e);

        }
    }

    public void processVideoFlowV2(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理视频整体理解任务: {}", cmsAsyncTask.getEsId());
        try {
            // 1. 查询视频信息
            CmsVideoInfo videoInfo = videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                    .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType())
                    .orderByDesc(CmsVideoInfo::getCreateTime)
                    .last("limit 1"));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            // 2. 解析异步结果
            String responseBody = JSONObject.toJSONString(asyncResultRequest.getData());

            queryResult2(responseBody, videoInfo);

            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);
        } catch (Exception e) {
            log.error("视频整体理解任务失败", e);
            videoInfoService.updateESVideoInfoStatusFailed(videoInfoService.getOne(new LambdaQueryWrapper<CmsVideoInfo>().eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())).getId(), e.getMessage());
        }
    }

    public void processVideoSplit(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        CmsVideoInfo videoInfo=null;
        try {
            log.info("开始处理视频分镜任务: {}", cmsAsyncTask.getEsId());
            // 1. 查询视频信息
            videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }

            //视频分割数据存储
            CmsVideoResult videoResult4 = new CmsVideoResult();
            videoResult4.setVideoId(videoInfo.getId());
            videoResult4.setType(VideoResultTypeEnum.VIDEO_SPLIT.getCode());
            videoResult4.setData(JSONObject.toJSONString(asyncResultRequest.getData()));
            videoResultService.save(videoResult4);

            // 3. 处理分镜数据并保存
            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.ALL_ASR.getCode()));
            VideoRecognition3Handle.ASR asr = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.ASR.class);

            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, cmsAsyncTask.getEsId());
            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
            boolean isOpen = false;
            if (dedupedData.getSourceType() == 1) {
                isOpen = true;
            }

            String compressMp4Path = FileDownloadUtil.getPath("compress_" + dedupedData.getEsId() + ".mp4");
            String fileName = dedupedData.getEsId() + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName);
            FileDownloadUtil.downloadFile3(fileService.getPicDownloadSignatureUrl(dedupedData.getVideoOssId()), localMp4FilePath);

            QianchuanVideoStrategy.Arg arg = JSONObject.parseObject(videoInfo.getArg(), QianchuanVideoStrategy.Arg.class);

            //判断localMp4FilePath和compressMp4Path本地是否存在视频，如果不存在就下载

            VideoRecognition3Handle.VideoSplitData splitData = JSONObject.parseObject(JSONObject.toJSONString(asyncResultRequest.getData()), VideoRecognition3Handle.VideoSplitData.class);

            handleVideoSplitData(splitData, compressMp4Path, asr, dedupedData, localMp4FilePath, videoInfo, isOpen, arg.getObservationId());

            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);
        } catch (Exception e) {
            log.error("视频分割任务失败", e);
            videoInfoService.updateESVideoInfoStatusFailed(videoInfo.getId(), e.getMessage());
        }

    }


    //异步分镜图片打标接口
    public void processSceneDecoding(CmsAsyncTask cmsAsyncTask, AsyncResultRequest asyncResultRequest) {
        log.info("开始处理分镜图片打标任务: {}", cmsAsyncTask.getEsId());
        String picFilePath = null;
        String segmentFilePath = null;
        CmsVideoInfo videoInfo = null;
        try {
            // 1. 查询视频信息
            videoInfo = videoInfoService.getOne(
                    new LambdaQueryWrapper<CmsVideoInfo>()
                            .eq(CmsVideoInfo::getEsId, cmsAsyncTask.getEsId())
                            .eq(CmsVideoInfo::getType, cmsAsyncTask.getVideoInfoType()));
            if (videoInfo == null) {
                throw new BusinessException("未找到对应的视频分析信息");
            }
            Map<String, String> map = JSONObject.parseObject(cmsAsyncTask.getData(), Map.class);
            int start = Integer.parseInt(map.get("start"));
            int end = Integer.parseInt(map.get("end"));
            int index = Integer.parseInt(map.get("index"));

            Map<String, String> sceneDecoding = (Map<String, String>) asyncResultRequest.getData();
            LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CmsPullTaskDedupedData::getEsId, cmsAsyncTask.getEsId());
            CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);

            //------------
            sceneDecoding.put("start", start + "");
            sceneDecoding.put("end", end + "");

//            String compressMp4Path = FileDownloadUtil.getPath("compress_" + dedupedData.getEsId()+ ".mp4");
            String fileName2 = dedupedData.getEsId() + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName2);
            FileDownloadUtil.downloadFile3(fileService.getPicDownloadSignatureUrl(dedupedData.getVideoOssId()), localMp4FilePath);

            //切视频片段
            String fileName = "segment_" + dedupedData.getEsId() + "_" + start + "_" + end + ".mp4";
            segmentFilePath = FileDownloadUtil.getPath(fileName);
            log.info("localMp4FilePath:{},segmentFilePath:{},start:{},end:{}", localMp4FilePath, segmentFilePath, start, end);
            VideoUtil.cutVideoSegment(localMp4FilePath, segmentFilePath, start, end);
            //上传视频片段到OSS
            String ossId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), fileName, false);

            uploadVideo(ossId, segmentFilePath);
            sceneDecoding.put("sceneDecodingSegmentOssId", ossId);
            //切出视频头图
            String picFileName = "pic_" + dedupedData.getEsId() + "_" + start + "_" + end + ".jpg";
            picFilePath = FileDownloadUtil.getPath(picFileName);
            VideoUtil.cutImage(segmentFilePath, picFilePath, 0);
            String picOssId = FilePathBuilder.getVideoDecodeOssPath(dedupedData.getEsId(), picFileName, false);


            sceneDecoding.put("sceneDecodingSegmentPicOssId", picOssId);
            uploadVideo(picOssId, picFilePath);

            if (start < 5000) {
                sceneDecoding.put("highlight", dedupedData.getHighlight() + "");
            }

            //从数据库查出ASR片段
            CmsVideoResult videoResultAsr = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.VIDEO_SPLIT.getCode()));
            VideoRecognition3Handle.VideoSplitData splitData = JSONObject.parseObject(videoResultAsr.getData(), VideoRecognition3Handle.VideoSplitData.class);
            CmsVideoResult videoResultASR2 = videoResultService.getOne(new LambdaQueryWrapper<CmsVideoResult>().eq(CmsVideoResult::getVideoId, videoInfo.getId()).eq(CmsVideoResult::getType, VideoResultTypeEnum.VIDEO_SPLIT_ASR.getCode()));
            List<QianchuanVideoStrategy.ASRFragment> asrFragmentList = JSONObject.parseArray(videoResultASR2.getData(), QianchuanVideoStrategy.ASRFragment.class);


            //把ASR片段加入到这个里面
            if (splitData.isUse_ASR()) {
                //加入台词，视频ASR分片信息
                qianchuanVideoStrategy.handleUserASR(sceneDecoding, asrFragmentList);
            } else {
                sceneDecoding.put("sceneDecodingOssId", ossId);
                sceneDecoding.put("sceneDecodingImageOssId", picOssId);
            }

            //保存数据
            CmsVideoResult videoResult = new CmsVideoResult();
            videoResult.setVideoId(videoInfo.getId());
            videoResult.setIndex(index);
            videoResult.setType(VideoResultTypeEnum.SCENE_SPLIT2.getCode());
            videoResult.setData(JSONObject.toJSONString(sceneDecoding));
            videoResultService.save(videoResult);

            //删除本地的素材
            FileDownloadUtil.deleteFile(picFilePath);
            FileDownloadUtil.deleteFile(segmentFilePath);

            //更新异步任务
            cmsAsyncTask.setTaskStatus(2);
            cmsAsyncTask.setUpdateTime(LocalDateTime.now());
            cmsAsyncTaskService.updateById(cmsAsyncTask);

            handleStatus(videoInfo, cmsAsyncTask);
        } catch (Exception e) {
            log.error("视频整体理解任务失败", e);
            videoInfoService.updateESVideoInfoStatusFailed(videoInfo.getId(), e.getMessage());
        } finally {
            //删除本地的素材
            FileDownloadUtil.deleteFile(picFilePath);
            FileDownloadUtil.deleteFile(segmentFilePath);
        }
    }


    private void handleVideoSplitData(VideoRecognition3Handle.VideoSplitData splitData, String compressMp4Path, VideoRecognition3Handle.ASR asr, CmsPullTaskDedupedData dedupedData, String localMp4FilePath, CmsVideoInfo cmsVideoInfo, Boolean isOpen, String observationId) {
        Map<String, VideoRecognition3Handle.ASRFragment> map = splitData.getASR();
        List<QianchuanVideoStrategy.ASRFragment> asrFragmentList = qianchuanVideoStrategy.handleUserASRPre(map, dedupedData.getEsId(), isOpen);

        //存入Result表
        CmsVideoResult videoResult = new CmsVideoResult();
        videoResult.setVideoId(cmsVideoInfo.getId());
        videoResult.setType(VideoResultTypeEnum.VIDEO_SPLIT_ASR.getCode());
        videoResult.setData(JSONObject.toJSONString(asrFragmentList));
        videoResultService.save(videoResult);

        List<List<Integer>> list = splitData.get画面分镜();
        Long videoLength = VideoUtil.getVideoLength(compressMp4Path) * 1000;
        dedupedData.setLongVideoDuration(videoLength);
        dedupedData.setUpdateTime(new Date());
        pullTaskDedupedDataService.updateById(dedupedData);

        int index = 0;
        for (List<Integer> time : list) {
            Integer start = time.get(0);
            Integer end = time.get(1);
            if (end > videoLength) {
                end = videoLength.intValue();
            }
            //5: 分镜图片打标接口
            VideoRecognition3Handle.AsyncData asyncData = sceneDecoding(compressMp4Path, asr, start, end, dedupedData, observationId);

            index++;
            Map<String, String> data = new HashMap<>();
            data.put("index", index + "");
            data.put("start", start + "");
            data.put("end", end + "");
            cmsAsyncTaskService.save(asyncData.getDb_unique_id(), AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoCode(), 1, dedupedData.getEsId(), "async_scene_decoding", JSONObject.toJSONString(data));
        }
    }


    private VideoRecognition3Handle.AsyncData sceneDecoding(String compressMp4Path, VideoRecognition3Handle.ASR asr, Integer start, Integer end, CmsPullTaskDedupedData dedupedData, String observationId) {
        // 5: 分镜图片打标接口
        VideoRecognition3Handle.SceneDecodingRequest sceneDecodingRequest = new VideoRecognition3Handle.SceneDecodingRequest();
        sceneDecodingRequest.setImages(List.of(
                VideoUtil.cutImageOfBase64(compressMp4Path, start),
                VideoUtil.cutImageOfBase64(compressMp4Path, (start + end) / 2),
                VideoUtil.cutImageOfBase64(compressMp4Path, end)
        ));
        sceneDecodingRequest.setSentences(getASR(asr, start, end));
        sceneDecodingRequest.setTitle(dedupedData.getTextTitle());
        sceneDecodingRequest.setContent(null);
        sceneDecodingRequest.setTask_queue(TASK_QUEUE);
        VideoRecognition3Handle.SceneDecodingResponse sceneDecodingResponse = videoRecognition3Handle.sceneDecoding(sceneDecodingRequest, observationId);
        VideoRecognition3Handle.AsyncData data = sceneDecodingResponse.getData();
        return data;
    }


    private void uploadVideo(String ossId, String localFilePath) {
        try {
            cmsS3FlowService.upload(ossId, new File(localFilePath));
        } catch (Exception e) {
            log.error("上传视频图片失败", e);
            throw new BusinessException("上传视频图片失败");
        }
    }

    public String getASR(VideoRecognition3Handle.ASR asr, Integer start, Integer end) {
        if (asr == null || asr.getSentences() == null || asr.getSentences().isEmpty()) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (VideoRecognition3Handle.Sentences sentence : asr.getSentences()) {
            if (sentence.getStart() >= start && sentence.getStart() < end) {
                result.append(sentence.getText());
            }

        }
        return result.toString();

    }

    @Data
    @AllArgsConstructor
    public static class ASR5 {
        private String asr5;
    }

    private void handleStatus(CmsVideoInfo videoInfo, CmsAsyncTask cmsAsyncTask) {
        LambdaQueryWrapper<CmsPullTaskDedupedData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CmsPullTaskDedupedData::getEsId, cmsAsyncTask.getEsId());
        CmsPullTaskDedupedData dedupedData = pullTaskDedupedDataService.getOne(queryWrapper);
        Boolean status = cmsAsyncTaskService.AllStatusSuccess(cmsAsyncTask.getEsId(), cmsAsyncTask.getVideoInfoType());
        if (status) {
            videoInfo.setStatus(3);
            videoInfo.setUpdateTime(new Date());
            videoInfoService.updateById(videoInfo);

            dedupedData.setAnalysisStatus(2);
            dedupedData.setUpdateTime(new Date());
            pullTaskDedupedDataService.updateById(dedupedData);
            String esId = cmsAsyncTask.getEsId();

            Arg arg = JSONObject.parseObject(videoInfo.getArg(), Arg.class);

            try {
                if (videoInfo.getType().equals(4)) {
                    String filename = dedupedData.getTextContent() == null ? "esId: " + dedupedData.getEsId() : dedupedData.getTextContent();
                    tokenUseDetailService.countTokenUse(arg.getObservationId(), videoInfo.getId(), 1, filename,
                            videoInfo.getTenantId(), videoInfo.getUserId().toString(), null);
                }
            } catch (Exception e) {
                log.error("记录token使用失败", e);
            }

            //删除本地视频
            String compressMp4Path = FileDownloadUtil.getPath("compress_" + esId + ".mp4");
            String fileName = esId + ".mp4";
            String localMp4FilePath = FileDownloadUtil.getPath(fileName);
            FileDownloadUtil.deleteFile(localMp4FilePath);
            FileDownloadUtil.deleteFile(compressMp4Path);


            //删除oss视频
            try {
                fileService.deleteFile(arg.getCompressMp4OSSId());
            } catch (Exception e) {
                log.error("删除oss视频失败:{}", arg.getCompressMp4OSSId());
            }
        }
    }


}
