//package cn.mlamp.insightflow.cms.strategy.video.create;
//
//import cn.mlamp.insightflow.cms.enums.AnalysisVideoTypeEnum;
//import jakarta.annotation.PostConstruct;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @Author: husuper
// * @CreateTime: 2025-05-30
// */
//@Component
//public class StrategyBeanConfig {
//
//    @Autowired
//    private QianchuanVideoStrategy qianchuanVideoStrategy;
//
//    @Autowired
//    private VideoAnalysis3Strategy videoAnalysis3Strategy;
//
//    @PostConstruct
//    public void init() {
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.QIANCHUAN_VIDEO.getVideoType(), qianchuanVideoStrategy);
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.VIDEO_ANALYSIS.getVideoType(), videoAnalysis3Strategy);
//        AnalysisVideoStrategyMap.register(AnalysisVideoTypeEnum.UPLOAD_VIDEO.getVideoType(), videoAnalysis3Strategy);
//    }
//
//
//
//}
