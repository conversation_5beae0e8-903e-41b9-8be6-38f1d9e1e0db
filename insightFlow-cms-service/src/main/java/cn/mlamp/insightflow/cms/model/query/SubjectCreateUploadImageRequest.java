package cn.mlamp.insightflow.cms.model.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: renguangzong
 * @CreateTime: 2025-05-12
 */
@Data
public class SubjectCreateUploadImageRequest implements Serializable {
    /**
     * 主体ID， 当存在时，表示编辑主体
     */
    @Schema(description = "主体id", required = false)
    private Long subjectId;

    @Schema(description = "主体图片ossId", required = true)
    private List<String> ossIds;

    @Schema(description = "主体姓名", required = true)
    private String subjectName;

    @Schema(description = "主体标签", required = false)
    private String subjectTag;

    @Schema(description = "主体可见性: 0 个人可见 1 租户可见", required = true)
    private Integer visibility;

}
