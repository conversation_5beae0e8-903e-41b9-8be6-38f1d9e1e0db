
package cn.mlamp.insightflow.cms.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;

import cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo;
import cn.mlamp.insightflow.cms.model.vo.BrandProductCountVO;

@Mapper
public interface QianchuanVideoHotspotMapper extends BaseMapper<QianchuanMaterialVideo> {

    /**
     * 查询品牌出现频率并按数量降序排序
     *
     * @param keyword   关键词
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 品牌及其出现次数列表
     */
    @Select("SELECT brand as name, COUNT(*) as count FROM cms_qianchuan_material_video "
            + "WHERE is_deleted = 0 AND analysis_status = 2 " + "AND brand IS NOT NULL AND brand != '' "
            + "AND brand LIKE CONCAT('%', #{keyword}, '%') "
            + "AND (#{startTime} IS NULL OR publish_time >= #{startTime}) "
            + "AND (#{endTime} IS NULL OR publish_time <= #{endTime}) "
            + "GROUP BY brand ORDER BY count DESC LIMIT #{limit}")
    List<BrandProductCountVO> getBrandCountList(@Param("keyword") String keyword, @Param("startTime") Date startTime,
            @Param("endTime") Date endTime, @Param("limit") Integer limit);

    /**
     * 查询产品出现频率并按数量降序排序
     *
     * @param keyword   关键词
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param limit     限制数量
     * @return 产品及其出现次数列表
     */
    @Select("SELECT product_name as name, COUNT(*) as count FROM cms_qianchuan_material_video "
            + "WHERE is_deleted = 0 AND analysis_status = 2 " + "AND product_name IS NOT NULL AND product_name != '' "
            + "AND product_name LIKE CONCAT('%', #{keyword}, '%') "
            + "AND (#{startTime} IS NULL OR publish_time >= #{startTime}) "
            + "AND (#{endTime} IS NULL OR publish_time <= #{endTime}) "
            + "GROUP BY product_name ORDER BY count DESC LIMIT #{limit}")
    List<BrandProductCountVO> getProductCountList(@Param("keyword") String keyword, @Param("startTime") Date startTime,
            @Param("endTime") Date endTime, @Param("limit") Integer limit);

    IPage<QianchuanMaterialVideo> listVideosByBrandSpellIntegrated(
            @Param("page") IPage<QianchuanMaterialVideo> page,
            @Param(Constants.WRAPPER) LambdaQueryWrapper<QianchuanMaterialVideo> wrapper,
            @Param("keyword") String keyword,
            @Param("brandSpell") String brandSpell
    );

    IPage<QianchuanMaterialVideo> listVideosByBrandSpellAndBrand(
            @Param("page") IPage<QianchuanMaterialVideo> page,
            @Param(Constants.WRAPPER) LambdaQueryWrapper<QianchuanMaterialVideo> wrapper,
            @Param("keyword") String keyword,
            @Param("brandSpell") String brandSpell,
            @Param("brand") String brand
    );

    IPage<QianchuanMaterialVideo> listVideos(
            @Param("page") IPage<QianchuanMaterialVideo> page,
            @Param("ew") LambdaQueryWrapper<QianchuanMaterialVideo> wrapper,
            @Param("brandSpell") String brandSpell
    );

}
