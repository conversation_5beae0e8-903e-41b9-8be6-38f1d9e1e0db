package cn.mlamp.insightflow.cms.controller.dam;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.mlamp.insightflow.cms.common.resp.RespBody;
import cn.mlamp.insightflow.cms.config.UserContext;
import cn.mlamp.insightflow.cms.enums.dam.DamTagTypeEnum;
import cn.mlamp.insightflow.cms.model.dto.dam.DamTagDTO;
import cn.mlamp.insightflow.cms.model.vo.dam.DamTagVO;
import cn.mlamp.insightflow.cms.service.dam.IDamTagService;
import jakarta.validation.Valid;

/**
 * DAM标签Controller
 */
@RestController
@RequestMapping("/dam")
public class DamTagController {

    @Autowired
    private IDamTagService tagService;

    /**
     * 创建自定义标签
     */
    @PostMapping("/custom-tags")
    public RespBody<DamTagVO> createCustomTag(@RequestBody @Valid DamTagDTO tagDTO) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        tagDTO.validate();
        final DamTagVO tagVO = tagService.createCustomTag(tagDTO, userId, tenantId);
        return RespBody.ok(tagVO);
    }

    /**
     * 获取标签列表
     */
    @GetMapping("/tags")
    public RespBody<List<DamTagVO>> getTagList(@RequestParam(required = false) Integer type,
            @RequestParam(required = false, defaultValue = "false") Boolean suggestValues) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        // 获取标签列表
        List<DamTagVO> tagList = tagService.getTagList(DamTagTypeEnum.getByCode(type), suggestValues, userId, tenantId);
        return RespBody.ok(tagList);
    }

    /**
     * 搜索标签
     */
    @GetMapping("/tags/search")
    public RespBody<List<DamTagVO>> searchTags(@RequestParam String keyword) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();
        List<DamTagVO> tagList = tagService.searchTags(keyword, userId, tenantId);
        return RespBody.ok(tagList);
    }

    /**
     * 更新标签
     */
    @PutMapping("/tags/{tagId}")
    public RespBody<DamTagVO> updateTag(@PathVariable Integer tagId,
            @RequestBody @Valid DamTagDTO tagDTO) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        DamTagVO tagVO = tagService.updateTag(tagId, tagDTO, userId, tenantId);
        return RespBody.ok(tagVO);
    }

    /**
     * 删除标签
     */
    @DeleteMapping("/tags/{tagId}")
    public RespBody<Void> deleteTag(@PathVariable Integer tagId) {
        // 从请求中获取用户ID和租户ID，实际情况应该从认证信息中获取
        Integer userId = UserContext.getUserId();
        Integer tenantId = UserContext.getTenantId();

        tagService.deleteTag(tagId, userId, tenantId);
        
        return RespBody.ok();
    }

}