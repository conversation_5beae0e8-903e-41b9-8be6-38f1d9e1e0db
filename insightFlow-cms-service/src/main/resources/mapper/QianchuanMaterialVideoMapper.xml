<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mlamp.insightflow.cms.mapper.QianchuanVideoHotspotMapper">

    <resultMap id="BaseResultMap" type="cn.mlamp.insightflow.cms.entity.QianchuanMaterialVideo">
        <id property="id" column="id" jdbcType="INTEGER" />
        <result property="ossid" column="ossid" jdbcType="VARCHAR" />
        <result property="videoId" column="video_id" jdbcType="VARCHAR" />
        <result property="consumeRange" column="consume_range" jdbcType="VARCHAR" />
        <result property="consumeRangeWeight" column="consume_range_weight" jdbcType="INTEGER" />
        <result property="title" column="title" jdbcType="VARCHAR" />
        <result property="brand" column="brand" jdbcType="VARCHAR" />
        <result property="brandSpell" column="brand_spell" jdbcType="VARCHAR" />
        <result property="exposure" column="exposure" jdbcType="INTEGER" />
        <result property="shares" column="shares" jdbcType="INTEGER" />
        <result property="comments" column="comments" jdbcType="INTEGER" />
        <result property="likes" column="likes" jdbcType="INTEGER" />
        <result property="clicks" column="clicks" jdbcType="INTEGER" />
        <result property="industry" column="industry" jdbcType="VARCHAR" />
        <result property="ourIndustry" column="our_industry" jdbcType="VARCHAR" />
        <result property="kwVideoContent" column="kw_video_content" jdbcType="VARCHAR" />
        <result property="rankingType" column="ranking_type" jdbcType="VARCHAR" />
        <result property="publishTime" column="publish_time" jdbcType="TIMESTAMP" />
        <result property="authorName" column="author_name" jdbcType="VARCHAR" />
        <result property="authorAvatar" column="author_avatar" jdbcType="VARCHAR" />
        <result property="coverImage" column="cover_image" jdbcType="VARCHAR" />
        <result property="duration" column="duration" jdbcType="INTEGER" />
        <result property="ourDuration" column="our_duration" jdbcType="INTEGER" />
        <result property="completionRate" column="completion_rate" jdbcType="DECIMAL" />
        <result property="ctr" column="ctr" jdbcType="DECIMAL" />
        <result property="isDeleted" column="is_deleted" jdbcType="TINYINT" />
        <result property="rating" column="rating" jdbcType="FLOAT" />
        <result property="productName" column="product_name" jdbcType="VARCHAR" />
        <result property="cellingPoint" column="celling_point" jdbcType="VARCHAR" />
        <result property="aimingTribe" column="aiming_tribe" jdbcType="VARCHAR" />
        <result property="highlight" column="highlight" jdbcType="INTEGER" />
        <result property="analysisStatus" column="analysis_status" jdbcType="INTEGER" />
        <!-- BaseEntity 继承的字段 -->
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
        id, ossid, video_id, consume_range, consume_range_weight, title, brand, brand_spell,
        exposure, shares, comments, likes, clicks, industry, our_industry, kw_video_content,
        ranking_type, publish_time, author_name, author_avatar, cover_image, duration, our_duration,
        completion_rate, ctr, is_deleted, rating, product_name, celling_point, aiming_tribe,
        highlight, analysis_status, create_time, update_time
    </sql>

    <select id="listVideosByBrandSpellIntegrated" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
            ,
            CASE
                WHEN (brand_spell IS NOT NULL AND (#{brandSpell} LIKE CONCAT ('%', brand_spell, '%') OR brand_spell LIKE CONCAT('%', #{brandSpell}, '%'))) OR (brand LIKE CONCAT('%', #{keyword}, '%')) THEN 1
                ELSE 0
            END AS brand_spell_match
        FROM
            cms_qianchuan_material_video
        <bind name="whereClause" value="ew.customSqlSegment.replaceAll('(?i)\\s*ORDER\\s+BY.*$', '').trim()" />
        <if test="whereClause != null and whereClause != ''">
            ${whereClause}
        </if>
            AND (
                ((brand_spell IS NOT NULL AND (#{brandSpell} LIKE CONCAT ('%', brand_spell, '%') OR brand_spell LIKE CONCAT('%', #{brandSpell}, '%'))) OR (brand LIKE CONCAT('%', #{keyword}, '%'))) OR
                title LIKE CONCAT('%', #{keyword}, '%') OR
                kw_video_content LIKE CONCAT('%', #{keyword}, '%') OR
                product_name LIKE CONCAT('%', #{keyword}, '%')
            )
        ORDER BY brand_spell_match DESC, consume_range_weight DESC
    </select>

    <select id="listVideosByBrandSpellAndBrand" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        ,
        CASE
        WHEN (brand_spell IS NOT NULL AND (#{brandSpell} LIKE CONCAT ('%', brand_spell, '%') OR brand_spell LIKE CONCAT('%', #{brandSpell}, '%'))) OR (brand LIKE CONCAT('%', #{keyword}, '%')) THEN 1
        ELSE 0
        END AS brand_spell_match
        FROM
        cms_qianchuan_material_video
        <bind name="whereClause" value="ew.customSqlSegment.replaceAll('(?i)\\s*ORDER\\s+BY.*$', '').trim()" />
        <if test="whereClause != null and whereClause != ''">
            ${whereClause}
        </if>
        AND (
        ((brand_spell IS NOT NULL AND (#{brandSpell} LIKE CONCAT ('%', brand_spell, '%') OR brand_spell LIKE CONCAT('%', #{brandSpell}, '%'))) OR (brand LIKE CONCAT('%', #{keyword}, '%')))
        )
        ORDER BY brand_spell_match DESC, consume_range_weight DESC
    </select>

    <select id="listVideos" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
            ,
            CASE
                WHEN brand_spell IS NOT NULL AND #{brandSpell} IS NOT NULL AND #{brandSpell} LIKE CONCAT ('%', brand_spell, '%') THEN 1
                ELSE 0
            END AS brand_spell_match
        FROM
            cms_qianchuan_material_video
        <where>
            <if test="ew.customSqlSegment != null and ew.customSqlSegment != ''">
                <bind name="whereClause" value="ew.customSqlSegment.replaceAll('(?i)\\s*ORDER\\s+BY.*$', '').trim()" />
                <if test="whereClause != null and whereClause != ''">
                    AND ${whereClause}
                </if>
            </if>
        </where>
        <choose>
            <when test="brandSpell != null and brandSpell != ''">
                ORDER BY brand_spell_match DESC,
                <if test="ew.customSqlSegment != null and ew.customSqlSegment != '' and ew.customSqlSegment.matches('(?i).*ORDER\\s+BY.*')">
                    <bind name="orderClause" value="ew.customSqlSegment.replaceAll('(?i)^.*ORDER\\s+BY\\s+', '').trim()" />
                    ${orderClause},
                </if>
                id DESC
            </when>
            <otherwise>
                <choose>
                    <when test="ew.customSqlSegment != null and ew.customSqlSegment != '' and ew.customSqlSegment.matches('(?i).*ORDER\\s+BY.*')">
                        <bind name="orderClause" value="ew.customSqlSegment.replaceAll('(?i)^.*ORDER\\s+BY\\s+', '').trim()" />
                        ORDER BY ${orderClause}
                    </when>
                    <otherwise>
                        ORDER BY id DESC
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

</mapper> 