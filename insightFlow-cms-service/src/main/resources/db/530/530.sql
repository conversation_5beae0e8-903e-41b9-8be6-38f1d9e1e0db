
-- 前川数据去除重复，需要添加联合索引，需要根据title和duration来判断是否重复
ALTER TABLE cms_qianchuan_material_video ADD INDEX idx_title_duration (title, duration);

-- 添加自定义视频时长字段，区别于原有的duration字段
ALTER TABLE cms_qianchuan_material_video ADD COLUMN `our_duration` int(11) DEFAULT NULL COMMENT '自定义视频时长（单位：秒）';

-- 数据迁移：将原有duration字段的值复制到新的our_duration字段
UPDATE cms_qianchuan_material_video SET our_duration = duration WHERE duration IS NOT NULL;

-- 添加自定义行业字段，区别于原有的industry字段
ALTER TABLE cms_qianchuan_material_video ADD COLUMN `our_industry` varchar(64) DEFAULT NULL COMMENT '自定义行业分类';

-- 更新our_industry字段，根据industry字段进行映射
UPDATE cms_qianchuan_material_video
SET our_industry = CASE
    WHEN industry = '美妆' THEN '护肤品'
    WHEN industry = '鞋服箱包' THEN '内衣家居服'
    WHEN industry = '日化' THEN '个人护理'
    WHEN industry = '食品饮料' THEN '饮料乳品'
    WHEN industry = '医疗健康' THEN '滋补品'
    WHEN industry = '母婴' THEN '母婴食品'
    WHEN industry = '3C家电' THEN '3C/手机'
    ELSE industry  -- 其他未映射的行业保持原值
END
WHERE industry IS NOT NULL;

-- 添加点击率字段，和完播率字段类型一致
ALTER TABLE cms_qianchuan_material_video ADD COLUMN `ctr` decimal(5,2) DEFAULT NULL COMMENT '点击率，百分比';

-- 添加素材形式字段
ALTER TABLE cms_qianchuan_material_video ADD COLUMN `material_form` varchar(64) DEFAULT NULL COMMENT '素材形式';
