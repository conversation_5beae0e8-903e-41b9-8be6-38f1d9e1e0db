package cn.mlamp.insightflow.cms.util;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

@DisplayName("拼音工具类测试")
class PinyinUtilsTest {

    @Test
    @DisplayName("测试纯中文转拼音")
    void testPureChineseToPinyin() {
        // 测试常见汉字
        assertEquals("zhongguo", PinyinUtils.toPinyin("中国"));
        assertEquals("beijing", PinyinUtils.toPinyin("北京"));
        assertEquals("shanghai", PinyinUtils.toPinyin("上海"));
        assertEquals("guangzhou", PinyinUtils.toPinyin("广州"));
        assertEquals("shenzhen", PinyinUtils.toPinyin("深圳"));
    }

    @Test
    @DisplayName("测试单个汉字转拼音")
    void testSingleChineseToPinyin() {
        assertEquals("ni", PinyinUtils.toPinyin("你"));
        assertEquals("hao", PinyinUtils.toPinyin("好"));
        assertEquals("shi", PinyinUtils.toPinyin("世"));
        assertEquals("jie", PinyinUtils.toPinyin("界"));
        assertEquals("ren", PinyinUtils.toPinyin("人"));
    }

    @Test
    @DisplayName("测试中英文混合转拼音")
    void testChineseEnglishMixedToPinyin() {
        assertEquals("helloshijie", PinyinUtils.toPinyin("hello世界"));
        assertEquals("zhongguoChina", PinyinUtils.toPinyin("中国China"));
        assertEquals("InsightFlowneirong", PinyinUtils.toPinyin("InsightFlow内容"));
        assertEquals("2023nian", PinyinUtils.toPinyin("2023年"));
    }

    @Test
    @DisplayName("测试纯英文字符")
    void testPureEnglishToPinyin() {
        assertEquals("hello", PinyinUtils.toPinyin("hello"));
        assertEquals("world", PinyinUtils.toPinyin("world"));
        assertEquals("InsightFlow", PinyinUtils.toPinyin("InsightFlow"));
        assertEquals("CMS", PinyinUtils.toPinyin("CMS"));
    }

    @Test
    @DisplayName("测试数字和特殊字符")
    void testNumbersAndSpecialCharacters() {
        assertEquals("123", PinyinUtils.toPinyin("123"));
        assertEquals("abc123", PinyinUtils.toPinyin("abc123"));
        assertEquals("!@#$%", PinyinUtils.toPinyin("!@#$%"));
        assertEquals("hello-world", PinyinUtils.toPinyin("hello-world"));
        assertEquals("test_case", PinyinUtils.toPinyin("test_case"));
        assertEquals("<EMAIL>", PinyinUtils.toPinyin("<EMAIL>"));
    }

    @Test
    @DisplayName("测试包含标点符号的中文")
    void testChineseWithPunctuation() {
        assertEquals("ni,hao!", PinyinUtils.toPinyin("你,好!"));
        assertEquals("zhongguo。", PinyinUtils.toPinyin("中国。"));
        assertEquals("beijing、shanghai", PinyinUtils.toPinyin("北京、上海"));
        assertEquals("\"zhongguo\"", PinyinUtils.toPinyin("\"中国\""));
        assertEquals("(beijing)", PinyinUtils.toPinyin("(北京)"));
    }

    @Test
    @DisplayName("测试空字符串和null")
    void testEmptyAndNull() {
        assertEquals("", PinyinUtils.toPinyin(""));
        
        // 测试null情况 - 这里可能会抛出异常，需要根据实际需求决定是否需要处理
        assertThrows(NullPointerException.class, () -> {
            PinyinUtils.toPinyin(null);
        });
    }

    @Test
    @DisplayName("测试空白字符")
    void testWhitespaceCharacters() {
        assertEquals(" ", PinyinUtils.toPinyin(" "));
        assertEquals("  ", PinyinUtils.toPinyin("  "));
        assertEquals("\t", PinyinUtils.toPinyin("\t"));
        assertEquals("\n", PinyinUtils.toPinyin("\n"));
        assertEquals("ni hao", PinyinUtils.toPinyin("你 好"));
        assertEquals("zhong\tguo", PinyinUtils.toPinyin("中\t国"));
    }

    @ParameterizedTest
    @DisplayName("参数化测试 - 常用词汇")
    @CsvSource({
        "你好, nihao",
        "世界, shijie",
        "中国, zhongguo",
        "北京, beijing",
        "上海, shanghai",
        "广州, guangzhou",
        "深圳, shenzhen",
        "杭州, hangzhou",
        "成都, chengdou",
        "西安, xian"
    })
    void testCommonWords(String input, String expected) {
        assertEquals(expected, PinyinUtils.toPinyin(input));
    }

    @ParameterizedTest
    @DisplayName("参数化测试 - 多音字（取第一个读音）")
    @CsvSource({
        "银行, yinxing",
        "行走, xingzou", 
        "重要, zhongyao",
        "重复, zhongfu",
        "长度, zhangdu",
        "成长, chengzhang"
    })
    void testPolyphonicCharacters(String input, String expected) {
        assertEquals(expected, PinyinUtils.toPinyin(input));
    }

    @ParameterizedTest
    @DisplayName("参数化测试 - 特殊字符保持不变")
    @ValueSource(strings = {"123", "abc", "!@#", "hello-world", "test_case", "<EMAIL>"})
    void testNonChineseCharactersRemainUnchanged(String input) {
        assertEquals(input, PinyinUtils.toPinyin(input));
    }

    @Test
    @DisplayName("测试长文本转拼音")
    void testLongTextToPinyin() {
        String longText = "InsightFlow是一款面向内容运营与创意分析的内容管理SaaS系统";
        String expected = "InsightFlowshiyikuanmianxiangneirongyunyingyuchuangyifenxideneirongguanliSaaSxitong";
        assertEquals(expected, PinyinUtils.toPinyin(longText));
    }

    @Test
    @DisplayName("测试包含数字的中文文本")
    void testChineseWithNumbers() {
        assertEquals("2023nian12yue25ri", PinyinUtils.toPinyin("2023年12月25日"));
        assertEquals("di1zhang", PinyinUtils.toPinyin("第1章"));
        assertEquals("100ge", PinyinUtils.toPinyin("100个"));
    }

    @Test
    @DisplayName("测试繁体字转拼音")
    void testTraditionalChineseToPinyin() {
        // 注意：这里的期望结果需要根据pinyin4j库的实际行为来确定
        // 如果库不支持繁体字，这些字符可能会保持不变
        String result1 = PinyinUtils.toPinyin("臺灣");
        String result2 = PinyinUtils.toPinyin("繁體字");
        
        // 验证结果不为空且不等于原始输入（如果库支持繁体字转换）
        assertNotNull(result1);
        assertNotNull(result2);
        assertFalse(result1.isEmpty());
        assertFalse(result2.isEmpty());
    }

    @Test
    @DisplayName("测试性能 - 大量文本转换")
    void testPerformanceWithLargeText() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("中国北京上海广州深圳");
        }
        String largeText = sb.toString();
        
        long startTime = System.currentTimeMillis();
        String result = PinyinUtils.toPinyin(largeText);
        long endTime = System.currentTimeMillis();
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        
        // 验证转换时间在合理范围内（这里设置为5秒，可根据实际需求调整）
        assertTrue(endTime - startTime < 5000, "转换时间过长: " + (endTime - startTime) + "ms");
    }
} 